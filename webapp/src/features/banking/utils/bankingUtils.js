/**
 * Enhanced Banking Utilities
 *
 * Utility functions for Malaysian banking operations, validation, and formatting.
 * Integrated with Malaysian Bank Service for comprehensive bank support.
 */

import malaysianBankService from '../services/MalaysianBankService';

// Enhanced bank account number validation patterns for Malaysian banks
const MALAYSIAN_BANK_PATTERNS = {
  // Major Malaysian banks with specific patterns
  MBB: {
    pattern: /^[0-9]{13}$/,
    format: '13 digits',
    example: '********90123',
    description: 'Maybank account number'
  },
  CIMB: {
    pattern: /^[0-9]{13}$/,
    format: '13 digits',
    example: '********90123',
    description: 'CIMB Bank account number'
  },
  PBB: {
    pattern: /^[0-9]{10,12}$/,
    format: '10-12 digits',
    example: '********90',
    description: 'Public Bank account number'
  },
  RHB: {
    pattern: /^[0-9]{14}$/,
    format: '14 digits',
    example: '********901234',
    description: 'RHB Bank account number'
  },
  HLB: {
    pattern: /^[0-9]{11,15}$/,
    format: '11-15 digits',
    example: '********901',
    description: 'Hong Leong Bank account number'
  },

  // Generic pattern for other Malaysian banks
  GENERIC: {
    pattern: /^[0-9]{8,20}$/,
    format: '8-20 digits',
    example: '********',
    description: 'Generic bank account number'
  }
};

// Account holder name validation
const ACCOUNT_HOLDER_PATTERNS = {
  // Malaysian name pattern (letters, spaces, apostrophes, hyphens)
  MALAYSIAN: /^[a-zA-Z\s'.-]{2,100}$/,
  // International name pattern (more permissive)
  INTERNATIONAL: /^[a-zA-Z\s'.-]{2,100}$/
};

/**
 * Validate Malaysian bank account number
 * @param {string} accountNumber - Account number to validate
 * @param {string} bankCode - Bank code (e.g., 'MBB', 'CIMB')
 * @returns {Object} Validation result
 */
export const validateMalaysianBankAccount = (accountNumber, bankCode) => {
  if (!accountNumber || !bankCode) {
    return {
      isValid: false,
      error: 'Account number and bank code are required',
      errorCode: 'MISSING_REQUIRED_FIELDS'
    };
  }

  // Clean account number (remove spaces, hyphens)
  const cleanAccountNumber = accountNumber.replace(/[\s-]/g, '');

  // Get bank pattern
  const bankPattern = MALAYSIAN_BANK_PATTERNS[bankCode] || MALAYSIAN_BANK_PATTERNS.GENERIC;

  // Validate against pattern
  if (!bankPattern.pattern.test(cleanAccountNumber)) {
    return {
      isValid: false,
      error: `Invalid account number format. Expected: ${bankPattern.format}`,
      errorCode: 'INVALID_FORMAT',
      expectedFormat: bankPattern.format,
      example: bankPattern.example
    };
  }

  return {
    isValid: true,
    cleanAccountNumber,
    formattedAccountNumber: formatAccountNumber(cleanAccountNumber, bankCode)
  };
};

/**
 * Validate account holder name
 * @param {string} name - Account holder name
 * @param {string} type - Validation type ('malaysian' or 'international')
 * @returns {Object} Validation result
 */
export const validateAccountHolderName = (name, type = 'malaysian') => {
  if (!name) {
    return {
      isValid: false,
      error: 'Account holder name is required',
      errorCode: 'MISSING_NAME'
    };
  }

  const trimmedName = name.trim();

  if (trimmedName.length < 2) {
    return {
      isValid: false,
      error: 'Name must be at least 2 characters long',
      errorCode: 'NAME_TOO_SHORT'
    };
  }

  if (trimmedName.length > 100) {
    return {
      isValid: false,
      error: 'Name must not exceed 100 characters',
      errorCode: 'NAME_TOO_LONG'
    };
  }

  const pattern = type === 'international'
    ? ACCOUNT_HOLDER_PATTERNS.INTERNATIONAL
    : ACCOUNT_HOLDER_PATTERNS.MALAYSIAN;

  if (!pattern.test(trimmedName)) {
    return {
      isValid: false,
      error: 'Name contains invalid characters. Only letters, spaces, apostrophes, and hyphens are allowed',
      errorCode: 'INVALID_CHARACTERS'
    };
  }

  return {
    isValid: true,
    cleanName: trimmedName.toUpperCase() // Malaysian banks typically use uppercase
  };
};

/**
 * Format account number for display
 * @param {string} accountNumber - Account number to format
 * @param {string} bankCode - Bank code
 * @returns {string} Formatted account number
 */
export const formatAccountNumber = (accountNumber, bankCode) => {
  if (!accountNumber) return '';

  const clean = accountNumber.replace(/[\s-]/g, '');

  switch (bankCode) {
    case 'MBB': // Maybank: 1234-5678-90123
      return clean.replace(/(\d{4})(\d{4})(\d{5})/, '$1-$2-$3');

    case 'CIMB': // CIMB: 1234-5678-90123
      return clean.replace(/(\d{4})(\d{4})(\d{5})/, '$1-$2-$3');

    case 'PBB': // Public Bank: varies
      if (clean.length === 10) {
        return clean.replace(/(\d{3})(\d{3})(\d{4})/, '$1-$2-$3');
      } else if (clean.length === 12) {
        return clean.replace(/(\d{4})(\d{4})(\d{4})/, '$1-$2-$3');
      }
      return clean;

    case 'RHB': // RHB: 12345-678-901234
      return clean.replace(/(\d{5})(\d{3})(\d{6})/, '$1-$2-$3');

    case 'HLB': // Hong Leong: varies
      if (clean.length >= 11) {
        return clean.replace(/(\d{3})(\d{4})(\d+)/, '$1-$2-$3');
      }
      return clean;

    default:
      // Generic formatting: group by 4 digits
      return clean.replace(/(\d{4})/g, '$1-').replace(/-$/, '');
  }
};

/**
 * Mask account number for display (show only last 4 digits)
 * @param {string} accountNumber - Account number to mask
 * @returns {string} Masked account number
 */
export const maskAccountNumber = (accountNumber) => {
  if (!accountNumber) return '';

  const clean = accountNumber.replace(/[\s-]/g, '');

  if (clean.length <= 4) return clean;

  const lastFour = clean.slice(-4);
  const masked = '*'.repeat(clean.length - 4);

  return `${masked}${lastFour}`;
};

/**
 * Get bank account validation rules
 * @param {string} bankCode - Bank code
 * @returns {Object} Validation rules
 */
export const getBankValidationRules = (bankCode) => {
  const pattern = MALAYSIAN_BANK_PATTERNS[bankCode] || MALAYSIAN_BANK_PATTERNS.GENERIC;

  return {
    pattern: pattern.pattern,
    format: pattern.format,
    example: pattern.example,
    description: pattern.description,
    minLength: pattern.format.includes('-')
      ? parseInt(pattern.format.split('-')[0])
      : parseInt(pattern.format.split(' ')[0]),
    maxLength: pattern.format.includes('-')
      ? parseInt(pattern.format.split('-')[1])
      : parseInt(pattern.format.split(' ')[0])
  };
};

/**
 * Generate account number placeholder
 * @param {string} bankCode - Bank code
 * @returns {string} Placeholder text
 */
export const getAccountNumberPlaceholder = (bankCode) => {
  const rules = getBankValidationRules(bankCode);
  return `Enter ${rules.format} (e.g., ${rules.example})`;
};

/**
 * Validate complete bank account data
 * @param {Object} accountData - Bank account data
 * @returns {Object} Validation result
 */
export const validateBankAccountData = async (accountData) => {
  const errors = {};

  // Validate bank selection
  if (!accountData.malaysian_bank_id) {
    errors.malaysian_bank_id = 'Please select a bank';
  }

  // Validate account number
  if (!accountData.account_number) {
    errors.account_number = 'Account number is required';
  } else if (accountData.malaysian_bank_id) {
    try {
      const bank = await malaysianBankService.getBankById(accountData.malaysian_bank_id);
      if (bank) {
        const validation = await malaysianBankService.validateAccountNumber(
          accountData.account_number,
          accountData.malaysian_bank_id
        );

        if (!validation.isValid) {
          errors.account_number = validation.error;
        }
      }
    } catch (error) {
      console.error('Bank validation failed:', error);
    }
  }

  // Validate account holder name
  if (!accountData.account_holder_name) {
    errors.account_holder_name = 'Account holder name is required';
  } else {
    const nameValidation = validateAccountHolderName(accountData.account_holder_name);
    if (!nameValidation.isValid) {
      errors.account_holder_name = nameValidation.error;
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * Format bank account for display
 * @param {Object} bankAccount - Bank account object
 * @param {Array} malaysianBanks - Array of Malaysian banks for lookup (optional)
 * @returns {Object} Formatted bank account
 */
export const formatBankAccountForDisplay = (bankAccount, malaysianBanks = []) => {
  if (!bankAccount) return null;

  // Try to get bank information from the account object first
  let bankInfo = bankAccount.bank;

  // If bank info is missing but we have malaysian_bank_id, try to look it up
  if (!bankInfo && bankAccount.malaysian_bank_id && malaysianBanks.length > 0) {
    console.log('🏦 formatBankAccountForDisplay: Looking up bank for ID:', bankAccount.malaysian_bank_id);
    const foundBank = malaysianBanks.find(b => b.id === bankAccount.malaysian_bank_id);
    if (foundBank) {
      console.log('🏦 formatBankAccountForDisplay: Found bank:', foundBank);
      bankInfo = {
        id: foundBank.id,
        name: foundBank.name,
        code: foundBank.code,
        logo_url: foundBank.logo_url || `/images/banks/${foundBank.code.toLowerCase()}.png`
      };
    } else {
      console.warn('🏦 formatBankAccountForDisplay: Bank not found for ID:', bankAccount.malaysian_bank_id);
      console.log('🏦 formatBankAccountForDisplay: Available banks:', malaysianBanks);
    }
  }

  // Fallback values if bank info is still missing
  const bankName = bankInfo?.name || `Bank ID ${bankAccount.malaysian_bank_id || 'Unknown'}`;
  const bankCode = bankInfo?.code || 'UNKNOWN';
  const bankLogo = bankInfo?.logo_url || '/images/banks/default.png';

  console.log('🏦 formatBankAccountForDisplay: Final bank info:', { bankName, bankCode, bankLogo });

  return {
    ...bankAccount,
    formattedAccountNumber: formatAccountNumber(
      bankAccount.account_number,
      bankCode
    ),
    maskedAccountNumber: maskAccountNumber(bankAccount.account_number),
    bankName,
    bankCode,
    bankLogo,
    // Include the bank object for consistency
    bank: bankInfo || {
      id: bankAccount.malaysian_bank_id,
      name: bankName,
      code: bankCode,
      logo_url: bankLogo
    }
  };
};

/**
 * Check if bank account is complete
 * @param {Object} bankAccount - Bank account object
 * @returns {boolean} Whether account is complete
 */
export const isBankAccountComplete = (bankAccount) => {
  return !!(
    bankAccount &&
    bankAccount.malaysian_bank_id &&
    bankAccount.account_number &&
    bankAccount.account_holder_name &&
    bankAccount.bank
  );
};

/**
 * Get bank account status
 * @param {Object} bankAccount - Bank account object
 * @returns {Object} Status information
 */
export const getBankAccountStatus = (bankAccount) => {
  if (!bankAccount) {
    return { status: 'missing', message: 'No bank account', color: 'gray' };
  }

  if (!isBankAccountComplete(bankAccount)) {
    return { status: 'incomplete', message: 'Incomplete information', color: 'orange' };
  }

  if (bankAccount.is_verified) {
    return { status: 'verified', message: 'Verified', color: 'green' };
  }

  return { status: 'pending', message: 'Pending verification', color: 'blue' };
};

export default {
  validateMalaysianBankAccount,
  validateAccountHolderName,
  formatAccountNumber,
  maskAccountNumber,
  getBankValidationRules,
  getAccountNumberPlaceholder,
  validateBankAccountData,
  formatBankAccountForDisplay,
  isBankAccountComplete,
  getBankAccountStatus,
  MALAYSIAN_BANK_PATTERNS,
  ACCOUNT_HOLDER_PATTERNS
};
