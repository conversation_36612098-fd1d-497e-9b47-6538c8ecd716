<?php

namespace App\Http\Controllers;

use App\Models\CreditPackage;
use App\Models\PaymentTransaction;
use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    protected $paymentService;
    
    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }
    
    /**
     * Retry a failed payment.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function retryPayment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'transaction_id' => 'required|string'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }
        
        $user = $request->user();
        $transactionId = $request->transaction_id;
        
        $transaction = PaymentTransaction::where('transaction_id', $transactionId)
            ->where('user_id', $user->id)
            ->first();
        
        if (!$transaction) {
            return response()->json([
                'message' => 'Transaction not found',
            ], 404);
        }
        
        if ($transaction->status === 'completed') {
            return response()->json([
                'message' => 'Payment already completed',
                'payment' => [
                    'transaction_id' => $transaction->transaction_id,
                    'status' => $transaction->status,
                    'failed_attempts' => $transaction->failed_int
                ]
            ]);
        }
        
        if ($transaction->status === 'completed') {
            return response()->json([
                'message' => 'Payment already completed',
                'payment' => [
                    'transaction_id' => $transaction->transaction_id,
                    'status' => $transaction->status,
                    'failed_attempts' => $transaction->failed_int
                ]
            ]);
        }
        
        $paymentUrl = $transaction->metadata['url'] ?? null;
        
        if (!$paymentUrl) {
            return response()->json([
                'message' => 'Payment URL not found in transaction metadata',
            ], 400);
        }
        
        return response()->json([
            'message' => 'Payment retry available',
            'payment' => [
                'transaction_id' => $transaction->transaction_id,
                'redirect_url' => $paymentUrl,
                'status' => $transaction->status,
                'failed_attempts' => $transaction->failed_int
            ]
        ]);
    }
    
    /**
     * Create a payment for a credit package.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createPayment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'credit_package_id' => 'required|exists:credit_packages,id',
            'redirect_url' => 'nullable|url'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }
        
        $user = $request->user();
        $creditPackage = CreditPackage::findOrFail($request->credit_package_id);
        
        // Check if the credit package is active and matches the user's country
        if (!$creditPackage->is_active) {
            return response()->json([
                'message' => 'Credit package is not available'
            ], 400);
        }
        
        if ($creditPackage->country_code !== $user->country_code && $user->country_code) {
            return response()->json([
                'message' => 'Credit package is not available in your country'
            ], 400);
        }
        
        try {
            $paymentDetails = $this->paymentService->createPayment(
                $user,
                $creditPackage,
                'billplz',
                [
                    'redirect_url' => $request->redirect_url
                ]
            );
            
            return response()->json([
                'message' => 'Payment created successfully',
                'payment' => $paymentDetails
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create payment',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Handle payment callback from Billplz.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function handleCallback(Request $request)
    {
        try {
            $transaction = $this->paymentService->processPaymentCallback($request->all(), 'billplz');
            
            return response()->json([
                'message' => 'Payment processed successfully',
                'status' => $transaction->status
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to process payment',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Handle payment return from Billplz.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function handleReturn(Request $request)
    {
        try {
            $data = [];
            
            $queryParams = $request->query();
            Log::debug('Raw query parameters', ['query' => $queryParams]);
            
            if ($request->has('billplz')) {
                $data = $request->input('billplz');
            } 
            else if (isset($queryParams['billplz%5Bid%5D'])) {
                $data = [
                    'id' => $queryParams['billplz%5Bid%5D'],
                    'paid' => $queryParams['billplz%5Bpaid%5D'] ?? null,
                    'paid_at' => $queryParams['billplz%5Bpaid_at%5D'] ?? null,
                    'x_signature' => $queryParams['billplz%5Bx_signature%5D'] ?? null
                ];
                
                foreach ($queryParams as $key => $value) {
                    if (strpos($key, 'billplz%5B') === 0) {
                        $paramName = str_replace(['billplz%5B', '%5D'], ['billplz[', ']'], $key);
                        $data[$paramName] = $value;
                    }
                }
            }
            else if (isset($queryParams['billplz[id]'])) {
                $data = [
                    'id' => $queryParams['billplz[id]'],
                    'paid' => $queryParams['billplz[paid]'] ?? null,
                    'paid_at' => $queryParams['billplz[paid_at]'] ?? null,
                    'x_signature' => $queryParams['billplz[x_signature]'] ?? null
                ];
                
                foreach ($queryParams as $key => $value) {
                    if (strpos($key, 'billplz[') === 0) {
                        $data[$key] = $value;
                    }
                }
            }
            else if (isset($queryParams['id'])) {
                $data = $request->only(['id', 'paid', 'paid_at', 'x_signature']);
            }
            else {
                foreach ($queryParams as $key => $value) {
                    if (strpos($key, 'billplz') === 0) {
                        $matches = [];
                        if (preg_match('/billplz\[([^\]]+)\]/', urldecode($key), $matches)) {
                            $paramName = $matches[1];
                            $data[$paramName] = $value;
                            $data['billplz[' . $paramName . ']'] = $value; // Keep original format for signature validation
                        }
                    }
                }
            }
            
            Log::info('Billplz return data', ['data' => $data, 'request' => $request->all()]);
            
            $filteredData = [];
            if (isset($data['id'])) $filteredData['id'] = $data['id'];
            if (isset($data['paid'])) $filteredData['paid'] = $data['paid'];
            if (isset($data['paid_at'])) $filteredData['paid_at'] = $data['paid_at'];
            if (isset($data['x_signature'])) $filteredData['x_signature'] = $data['x_signature'];
            
            if (isset($data['billplz']) && is_array($data['billplz'])) {
                $filteredData['billplz'] = [];
                if (isset($data['billplz']['id'])) $filteredData['billplz']['id'] = $data['billplz']['id'];
                if (isset($data['billplz']['paid'])) $filteredData['billplz']['paid'] = $data['billplz']['paid'];
                if (isset($data['billplz']['paid_at'])) $filteredData['billplz']['paid_at'] = $data['billplz']['paid_at'];
                if (isset($data['billplz']['x_signature'])) $filteredData['billplz']['x_signature'] = $data['billplz']['x_signature'];
            }
            
            Log::info('Filtered Billplz data for signature validation', ['filtered_data' => $filteredData]);
            
            $billId = $filteredData['id'] ?? ($filteredData['billplz']['id'] ?? null);
            $isPaid = isset($filteredData['paid']) ? ($filteredData['paid'] === 'true' || $filteredData['paid'] === true) : 
                     (isset($filteredData['billplz']['paid']) ? ($filteredData['billplz']['paid'] === 'true' || $filteredData['billplz']['paid'] === true) : false);
            
            if ($billId && !$isPaid) {
                $transaction = PaymentTransaction::where('transaction_id', $billId)
                    ->where('status', '!=', 'completed')
                    ->first();
                
                if ($transaction) {
                    $transaction->failed_int = ($transaction->failed_int ?? 0) + 1;
                    $transaction->save();
                    
                    Log::info('Updated failed payment counter', [
                        'transaction_id' => $transaction->id,
                        'bill_id' => $billId,
                        'failed_int' => $transaction->failed_int,
                        'status' => $transaction->status
                    ]);
                }
            }
            
            $transaction = $this->paymentService->processPaymentCallback($filteredData, 'billplz');
            
            return response()->json([
                'message' => 'Payment processed successfully',
                'status' => $transaction->status
            ]);
        } catch (\Exception $e) {
            Log::error('Payment return error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);
            
            return response()->json([
                'message' => 'Failed to process payment',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
