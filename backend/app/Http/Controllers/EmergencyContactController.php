<?php

namespace App\Http\Controllers;

use App\Http\Requests\EmergencyContact\StoreRequest;
use App\Models\EmergencyContact;
use Illuminate\Http\Request;

class EmergencyContactController extends Controller
{
    public function index(Request $request)
    {
        return $request->user()->emergencyContacts()
            ->with('relationship.translations')
            ->get();
    }

    public function store(StoreRequest $request)
    {
        if ($request->input('is_default')) {
            $request->user()->emergencyContacts()
                ->where('is_default', true)
                ->update(['is_default' => false]);
        }

        $contact = $request->user()->emergencyContacts()->create($request->validated());

        return response()->json($contact->load('relationship.translations'), 201);
    }

    public function update(StoreRequest $request, EmergencyContact $contact)
    {
        $this->authorize('update', $contact);

        if ($request->input('is_default')) {
            $request->user()->emergencyContacts()
                ->where('id', '!=', $contact->id)
                ->where('is_default', true)
                ->update(['is_default' => false]);
        }

        $contact->update($request->validated());

        return response()->json($contact->load('relationship.translations'));
    }

    public function destroy(EmergencyContact $contact)
    {
        $this->authorize('delete', $contact);
        
        $contact->delete();
        return response()->noContent();
    }
}
