<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Symfony\Component\Process\Process;
use Symfony\Component\Process\Exception\ProcessFailedException;
use App\Services\ImageProcessingService;
use App\Models\User;

class UserProfileController extends Controller
{
    /**
     * @var ImageProcessingService
     */
    private $imageProcessingService;
    
    /**
     * Create a new controller instance.
     */
    public function __construct(ImageProcessingService $imageProcessingService)
    {
        $this->imageProcessingService = $imageProcessingService;
    }
    public function getBiography(Request $request)
    {
        return response()->json([
            'biography' => $request->user()->biography
        ]);
    }

    public function updateBiography(Request $request)
    {
        $validated = $request->validate([
            'biography' => 'nullable|string'
        ]);

        $request->user()->update([
            'biography' => $validated['biography']
        ]);

        return response()->json([
            'message' => 'Biography updated successfully',
            'biography' => $request->user()->biography
        ]);
    }

    public function getProfile(Request $request)
    {
        $user = $request->user()->load('race.translations', 'personalities', 'languages', 'level');

        $locale = App::getLocale();

        $race = null;
        if ($user->race) {
            $translation = $user->race->translations->where('locale', $locale)->first();
            $race = [
                'id' => $user->race->id,
                'name' => $translation ? $translation->name : $user->race->name,
                'description' => $translation ? $translation->description : $user->race->description
            ];
        }

        $personalities = $user->personalities->map(function ($personality) use ($locale) {
            $translation = $personality->translations->where('locale', $locale)->first();
            return [
                'id' => $personality->id,
                'name' => $translation ? $translation->name : $personality->name,
                'description' => $translation ? $translation->description : $personality->description
            ];
        });

        $followersCount = $user->followers()->count();
        $followingCount = $user->following()->count();

        return response()->json([
            'id' => $user->id,
            'mobile_number' => $user->mobile_number,
            'email' => $user->email,
            'country_code' => $user->country_code,
            'nickname' => $user->nickname,
            'gender' => $user->gender,
            'date_of_birth' => $user->date_of_birth ? $user->date_of_birth->format('Y-m-d\TH:i:s.v\Z') : null,
            'constellation' => $user->constellation,
            'profile_picture' => $user->profile_picture,
            'voice_note' => $user->voice_note,
            'biography' => $user->biography,
            'role' => $user->role,
            'experience' => $user->experience,
            'race' => $race,
            'height' => $user->height,
            'weight' => $user->weight,
            'default_language' => $user->default_language,
            'allow_3rd_party_access' => $user->allow_3rd_party_access,
            'referral_code' => $user->referral_code,
            'languages' => $user->languages->map(function ($language) {
                return [
                    'id' => $language->id,
                    'name' => $language->name
                ];
            }),
            'personalities' => $personalities,
            'level' => $user->level ? [
                'id' => $user->level->id,
                'level' => $user->level->level,
                'name' => $user->level->name,
                'description' => $user->level->description,
                'indicator_image' => $user->level->indicator_image
            ] : null,
            'total_followers' => $followersCount,
            'following_count' => $followingCount,
            'is_following' => false // Always false for own profile
        ]);
    }

    public function updateProfile(Request $request)
    {
        $validated = $request->validate([
            'nickname' => 'nullable|string|max:20',
            'gender' => 'nullable|in:Male,Female',
            'height' => 'nullable|numeric',
            'weight' => 'nullable|numeric',
            'race_id' => 'nullable|exists:races,id',
            'date_of_birth' => 'nullable|date',
            'email' => 'nullable|email',
            'biography' => 'nullable|string',
            'voice_note' => 'nullable|file|mimes:mp3,wav,m4a,aac,ogg|max:5120',
            'profile_picture' => 'nullable|image|mimes:jpeg,png,jpg,heic,heif,webp|max:20480'
        ]);

        if ($request->hasFile('voice_note')) {
            $voiceNoteResponse = $this->uploadVoiceNote($request);

            if ($voiceNoteResponse->getStatusCode() !== 200) {
                return $voiceNoteResponse;
            }

            unset($validated['voice_note']);
        }

        if ($request->hasFile('profile_picture')) {
            $file = $request->file('profile_picture');
            $userId = $request->user()->id;

            try {
                if (!$this->imageProcessingService->validateImageContent($file)) {
                    return response()->json([
                        'message' => 'Invalid image content detected',
                        'error' => 'The uploaded file contains invalid or potentially malicious content'
                    ], 422)->header('Content-Type', 'application/json');
                }
                
                $existingProfilePicture = $request->user()->profile_picture;
                if ($existingProfilePicture && Storage::disk('cdn')->exists($existingProfilePicture)) {
                    Storage::disk('cdn')->delete($existingProfilePicture);
                }
                
                if ($this->imageProcessingService->isMobileImageFormat($file)) {
                    if (in_array(strtolower($file->getClientOriginalExtension()), ['heic', 'heif'])) {
                        $result = $this->imageProcessingService->processMobileImage($file, $userId, 'users');
                    } else {
                        $result = $this->imageProcessingService->processWebpImage($file, $userId, 'users');
                    }
                } else {
                    $result = $this->imageProcessingService->processImage($file, $userId, 'users');
                }
                
                $extension = pathinfo($result['optimized'], PATHINFO_EXTENSION);
                $profilePhotoPath = "users/{$userId}/profile_photo.{$extension}";
                
                Storage::disk('cdn')->copy($result['optimized'], $profilePhotoPath);
                Storage::disk('cdn')->delete($result['optimized']);
                
                $validated['profile_picture'] = $profilePhotoPath;
                
            } catch (\Exception $e) {
                Log::error('Error processing profile picture update', [
                    'user_id' => $userId,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                
                return response()->json([
                    'message' => 'Failed to update profile picture',
                    'error' => 'An error occurred while processing your image'
                ], 500)->header('Content-Type', 'application/json');
            }
            
            unset($validated['profile_picture']);
        }

        $request->user()->update($validated);

        return response()->json([
            'message' => 'Profile updated successfully',
            'user' => $this->getProfile($request)->getData()
        ])->header('Content-Type', 'application/json');
    }
    
    /**
     * Update user profile picture
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateProfilePicture(Request $request)
    {
        $request->validate([
            'profile_picture' => 'required|image|mimes:jpeg,png,jpg,heic,heif,webp|max:20480',
        ]);

        if (!$request->hasFile('profile_picture')) {
            return response()->json([
                'message' => 'No profile picture provided',
                'error' => 'A valid image file is required'
            ], 422)->header('Content-Type', 'application/json');
        }

        $file = $request->file('profile_picture');
        $userId = $request->user()->id;

        try {
            if (!$this->imageProcessingService->validateImageContent($file)) {
                return response()->json([
                    'message' => 'Invalid image content detected',
                    'error' => 'The uploaded file contains invalid or potentially malicious content'
                ], 422)->header('Content-Type', 'application/json');
            }
            
            $existingProfilePicture = $request->user()->profile_picture;
            if ($existingProfilePicture && Storage::disk('cdn')->exists($existingProfilePicture)) {
                Storage::disk('cdn')->delete($existingProfilePicture);
            }
            
            if ($this->imageProcessingService->isMobileImageFormat($file)) {
                if (in_array(strtolower($file->getClientOriginalExtension()), ['heic', 'heif'])) {
                    $result = $this->imageProcessingService->processMobileImage($file, $userId, 'users');
                } else {
                    $result = $this->imageProcessingService->processWebpImage($file, $userId, 'users');
                }
            } else {
                $result = $this->imageProcessingService->processImage($file, $userId, 'users');
            }
            
            $extension = pathinfo($result['optimized'], PATHINFO_EXTENSION);
            $profilePhotoPath = "users/{$userId}/profile_photo.{$extension}";
            
            Storage::disk('cdn')->copy($result['optimized'], $profilePhotoPath);
            
            Storage::disk('cdn')->delete($result['optimized']);
            
            $request->user()->update([
                'profile_picture' => $profilePhotoPath
            ]);
            
            return response()->json([
                'message' => 'Profile picture updated successfully',
                'profile_picture' => $profilePhotoPath
            ])->header('Content-Type', 'application/json');
            
        } catch (\Exception $e) {
            Log::error('Error processing profile picture update', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'message' => 'Failed to update profile picture',
                'error' => 'An error occurred while processing your image'
            ], 500)->header('Content-Type', 'application/json');
        }
    }

    /**
     * Get Allow 3rd Party Access setting.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAllow3rdPartyAccess()
    {
        $allow3rdPartyAccess = auth()->user()->allow_3rd_party_access;
        return response()->json($allow3rdPartyAccess);
    }

    /**
     * Update Allow 3rd Party Access setting.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateAllow3rdPartyAccess(Request $request)
    {
        $request->validate([
            'allow_3rd_party_access' => 'required|boolean',
        ]);

        $user = auth()->user();
        $user->allow_3rd_party_access = $request->allow_3rd_party_access;
        $user->save();

        return response()->json($user->allow_3rd_party_access);
    }

    /**
     * Upload voice note for user profile
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadVoiceNote(Request $request)
    {
        $request->validate([
            'voice_note' => 'required|file|mimes:mp3,wav,m4a,aac,ogg|max:5120', // Accept common formats, max 5MB
        ]);

        $user = $request->user();
        $userId = $user->id;

        if ($user->voice_note) {
            Storage::disk('cdn')->delete($user->voice_note);
        }

        $file = $request->file('voice_note');
        $tempPath = $file->getPathname();
        $outputPath = sys_get_temp_dir() . '/' . time() . '.aac';

        try {
            $process = new Process([
                'ffmpeg',
                '-i',
                $tempPath,
                '-c:a',
                'aac',
                '-b:a',
                '128k',
                '-t',
                '15', // Limit to 15 seconds
                $outputPath
            ]);

            $process->run();

            if (!$process->isSuccessful()) {
                throw new ProcessFailedException($process);
            }

            $cdnPath = "users/{$userId}/" . time() . '.aac';
            Storage::disk('cdn')->put($cdnPath, file_get_contents($outputPath));

            @unlink($outputPath);

            $user->update([
                'voice_note' => $cdnPath
            ]);

            return response()->json([
                'message' => 'Voice note uploaded successfully',
                'voice_note' => $cdnPath
            ]);
        } catch (\Exception $e) {
            Log::error('Voice note conversion failed: ' . $e->getMessage());

            if (file_exists($outputPath)) {
                @unlink($outputPath);
            }

            return response()->json([
                'message' => 'Failed to process voice note',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete voice note from user profile
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteVoiceNote(Request $request)
    {
        $user = $request->user();

        if ($user->voice_note) {
            Storage::disk('cdn')->delete($user->voice_note);

            $user->update([
                'voice_note' => null
            ]);

            return response()->json([
                'message' => 'Voice note deleted successfully'
            ]);
        }

        return response()->json([
            'message' => 'No voice note found'
        ], 404);
    }


    /**
     * Upload media for user profile (video, thumbnail, and photos)
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadMedia(Request $request)
    {
        $request->validate([
            'video' => 'nullable|file|mimes:mp4,mov,avi,flv,wmv,3gp,mkv|max:51200', // 50MB max for video
            'thumbnail' => 'nullable|file|mimes:jpeg,jpg,png,webp|max:5120', // 5MB max for thumbnail
            'photos.*' => 'nullable|file|mimes:jpeg,jpg,png,heic,heif,webp|max:20480', // 20MB max for photos
        ]);

        if (!$request->hasFile('video') && !$request->hasFile('thumbnail') && !$request->hasAny('photos')) {
            return response()->json([
                'message' => 'At least one media type (video, thumbnail, or photos) must be provided',
                'errors' => [
                    'media' => ['At least one media type (video, thumbnail, or photos) must be provided']
                ]
            ], 422);
        }

        if ($request->hasFile('photos') && count($request->file('photos')) > 5) {
            return response()->json([
                'message' => 'Maximum 5 photos allowed',
                'errors' => [
                    'photos' => ['Maximum 5 photos allowed']
                ]
            ], 422);
        }

        $user = $request->user();
        $media = $user->media ?? [];
        $response = [
            'message' => 'Profile media uploaded successfully',
            'media' => []
        ];

        if ($request->hasFile('video')) {
            $videoResponse = $this->processVideoUpload($request->file('video'), $user);

            if ($videoResponse->getStatusCode() !== 200) {
                return $videoResponse;
            }

            $response['media'] = $user->refresh()->media;
        }

        if ($request->hasFile('thumbnail') && !$request->hasFile('video')) {
            $thumbnailFile = $request->file('thumbnail');
            $userId = $user->id;

            try {
                $imageService = app(ImageProcessingService::class);
                $result = $imageService->processImage($thumbnailFile, $userId, 'profile/video');

                $media['thumbnail'] = "users/{$userId}/profile/video/thumbnail.jpg";

                $user->update([
                    'media' => $media
                ]);

                $response['media'] = $user->refresh()->media;
            } catch (\Exception $e) {
                Log::error('Thumbnail processing failed: ' . $e->getMessage(), [
                    'error_code' => $e->getCode(),
                    'error_file' => $e->getFile(),
                    'error_line' => $e->getLine(),
                    'user_id' => $userId
                ]);

                return response()->json([
                    'message' => 'Failed to process thumbnail',
                    'error' => $e->getMessage()
                ], 500);
            }
        }

        if ($request->hasFile('photos')) {
            foreach ($request->file('photos') as $photo) {
                $photoResponse = $this->processPhotoUpload($photo, $user);

                if ($photoResponse->getStatusCode() !== 200) {
                    return $photoResponse;
                }
            }

            $response['media'] = $user->refresh()->media;
        }

        return response()->json($response);
    }

    /**
     * Process video upload
     * 
     * @param \Illuminate\Http\UploadedFile $file
     * @param \App\Models\User $user
     * @return \Illuminate\Http\JsonResponse
     */
    private function processVideoUpload($file, $user)
    {
        $userId = $user->id;

        $media = $user->media ?? [];

        $videoPath = "users/{$userId}/profile/video/video.mp4";
        $thumbnailPath = "users/{$userId}/profile/video/thumbnail.jpg";

        if (Storage::disk('cdn')->exists($videoPath)) {
            Storage::disk('cdn')->delete($videoPath);
        }

        if (Storage::disk('cdn')->exists($thumbnailPath)) {
            Storage::disk('cdn')->delete($thumbnailPath);
        }

        if (!$file) {
            return response()->json([
                'message' => 'No media file provided',
                'error' => 'A valid media file is required'
            ], 422);
        }

        $tempPath = $file->getPathname();
        $outputPath = sys_get_temp_dir() . '/' . time() . '.mp4';
        $thumbnailPath = sys_get_temp_dir() . '/' . time() . '_thumbnail.jpg';

        try {
            $process = new Process([
                'ffmpeg',
                '-i',
                $tempPath,
                '-c:v',
                'libx264',
                '-c:a',
                'aac',
                '-b:a',
                '128k',
                '-t',
                '10', // Limit to 10 seconds
                '-vf',
                'scale=1280:-2',  // Scale to 720p maintaining aspect ratio
                '-movflags',
                '+faststart',
                $outputPath
            ]);

            $process->run();

            if (!$process->isSuccessful()) {
                throw new ProcessFailedException($process);
            }

            $thumbnailProcess = new Process([
                'ffmpeg',
                '-i',
                $outputPath,
                '-ss',
                '00:00:01', // Extract frame at 1 second mark
                '-vframes',
                '1',   // Extract only one frame
                '-q:v',
                '2',       // High quality
                '-vf',
                'scale=640:-2', // Scale to 640px width maintaining aspect ratio
                $thumbnailPath
            ]);

            $thumbnailProcess->run();

            if (!$thumbnailProcess->isSuccessful()) {
                throw new ProcessFailedException($thumbnailProcess);
            }

            $cdnPath = "users/{$userId}/profile/video/video.mp4";
            $thumbnailCdnPath = "users/{$userId}/profile/video/thumbnail.jpg";

            Storage::disk('cdn')->put($cdnPath, file_get_contents($outputPath));
            Storage::disk('cdn')->put($thumbnailCdnPath, file_get_contents($thumbnailPath));

            @unlink($outputPath);
            @unlink($thumbnailPath);

            $media['video'] = "users/{$userId}/profile/video/video.mp4";
            $media['thumbnail'] = "users/{$userId}/profile/video/thumbnail.jpg";

            $user->update([
                'media' => $media
            ]);

            return response()->json([
                'message' => 'Profile video uploaded successfully',
                'media' => $user->media
            ]);
        } catch (\Exception $e) {
            Log::error('Profile video processing failed: ' . $e->getMessage(), [
                'error_code' => $e->getCode(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'user_id' => $userId
            ]);

            if (file_exists($outputPath)) {
                @unlink($outputPath);
            }

            if (file_exists($thumbnailPath)) {
                @unlink($thumbnailPath);
            }

            return response()->json([
                'message' => 'Failed to process profile video',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process photo upload
     * 
     * @param \Illuminate\Http\UploadedFile $file
     * @param \App\Models\User $user
     * @return \Illuminate\Http\JsonResponse
     */
    private function processPhotoUpload($file, $user)
    {
        $userId = $user->id;

        $media = $user->media ?? [];
        $photos = $media['photos'] ?? [];

        if (count($photos) >= 5) {
            return response()->json([
                'message' => 'Maximum number of media photos (5) reached',
                'error' => 'Delete an existing photo before uploading a new one'
            ], 422);
        }

        if (!$file) {
            return response()->json([
                'message' => 'No media file provided',
                'error' => 'A valid media file is required'
            ], 422);
        }

        $imageService = app(ImageProcessingService::class);

        try {
            if ($imageService->isMobileImageFormat($file)) {
                if (in_array(strtolower($file->getClientOriginalExtension()), ['heic', 'heif'])) {
                    $result = $imageService->processMobileImage($file, $userId, 'profile/photos');
                } else {
                    $result = $imageService->processWebpImage($file, $userId, 'profile/photos');
                }
            } else {
                $result = $imageService->processImage($file, $userId, 'profile/photos');
            }

            $maxOrder = 0;
            foreach ($photos as $photo) {
                if (isset($photo['order']) && $photo['order'] > $maxOrder) {
                    $maxOrder = $photo['order'];
                }
            }

            $timestamp = time();
            $order = $maxOrder + 1;
            $extension = pathinfo($result['optimized'], PATHINFO_EXTENSION);
            $photoPath = "users/{$userId}/profile/photos/{$timestamp}_{$order}.{$extension}";

            Storage::disk('cdn')->copy($result['optimized'], $photoPath);

            Storage::disk('cdn')->delete($result['optimized']);

            $photos[] = [
                'path' => $photoPath,
                'original_path' => $result['original'],
                'order' => $order
            ];

            $media['photos'] = $photos;

            $user->update([
                'media' => $media
            ]);

            return response()->json([
                'message' => 'Media photo uploaded successfully',
                'media' => $user->media
            ]);
        } catch (\Exception $e) {
            Log::error('Media photo processing failed: ' . $e->getMessage(), [
                'error_code' => $e->getCode(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'user_id' => $userId
            ]);

            return response()->json([
                'message' => 'Failed to process media photo',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete media from user profile
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteMedia(Request $request)
    {
        $request->validate([
            'media_type' => 'required|in:video,photo',
            'order' => 'required_if:media_type,photo|integer|min:1'
        ]);

        $mediaType = $request->input('media_type');

        if ($mediaType === 'video') {
            return $this->deleteProfileVideo($request);
        } else {
            return $this->deleteMediaPhoto($request, $request->input('order'));
        }
    }

    /**
     * Delete profile video from user profile (internal method)
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    private function deleteProfileVideo(Request $request)
    {
        $user = $request->user();
        $userId = $user->id;
        $media = $user->media ?? [];

        $videoDeleted = false;
        $thumbnailDeleted = false;

        $videoPath = "users/{$userId}/profile/video/video.mp4";
        $thumbnailPath = "users/{$userId}/profile/video/thumbnail.jpg";

        if (Storage::disk('cdn')->exists($videoPath)) {
            Storage::disk('cdn')->delete($videoPath);
            unset($media['video']);
            $videoDeleted = true;
        }

        if (Storage::disk('cdn')->exists($thumbnailPath)) {
            Storage::disk('cdn')->delete($thumbnailPath);
            unset($media['thumbnail']);
            $thumbnailDeleted = true;
        }

        if ($videoDeleted || $thumbnailDeleted) {
            $user->update([
                'media' => $media
            ]);

            return response()->json([
                'message' => 'Profile video deleted successfully'
            ]);
        }

        return response()->json([
            'message' => 'No profile video found'
        ], 404);
    }

    /**
     * Delete media photo (internal method)
     * 
     * @param Request $request
     * @param int $order
     * @return \Illuminate\Http\JsonResponse
     */
    private function deleteMediaPhoto(Request $request, $order)
    {
        $user = $request->user();
        $media = $user->media ?? [];
        $photos = $media['photos'] ?? [];

        $photoIndex = null;
        $photoToDelete = null;

        foreach ($photos as $index => $photo) {
            if (isset($photo['order']) && $photo['order'] == $order) {
                $photoIndex = $index;
                $photoToDelete = $photo;
                break;
            }
        }

        if ($photoIndex !== null && $photoToDelete) {
            Storage::disk('cdn')->delete($photoToDelete['path']);

            if (isset($photoToDelete['original_path'])) {
                Storage::disk('cdn')->delete($photoToDelete['original_path']);
            }

            array_splice($photos, $photoIndex, 1);

            $reorderedPhotos = [];
            $newOrder = 1;

            foreach ($photos as $photo) {
                $photo['order'] = $newOrder++;
                $reorderedPhotos[] = $photo;
            }

            $media['photos'] = $reorderedPhotos;

            $user->update([
                'media' => $media
            ]);

            return response()->json([
                'message' => 'Media photo deleted successfully',
                'media' => $user->media
            ]);
        }

        return response()->json([
            'message' => 'Media photo not found'
        ], 404);
    }

    /**
     * Reorder profile media photos
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function reorderMedia(Request $request)
    {
        $request->validate([
            'photos' => 'required|array',
            'photos.*.order' => 'required|integer|min:1|max:5',
            'photos.*.new_order' => 'required|integer|min:1|max:5'
        ]);

        $user = $request->user();
        $media = $user->media ?? [];
        $photos = $media['photos'] ?? [];

        if (empty($photos)) {
            return response()->json([
                'message' => 'No media photos found'
            ], 404);
        }

        $photosByOrder = [];
        foreach ($photos as $photo) {
            if (isset($photo['order'])) {
                $photosByOrder[$photo['order']] = $photo;
            }
        }

        foreach ($request->photos as $photoData) {
            $currentOrder = $photoData['order'];
            $newOrder = $photoData['new_order'];

            if (isset($photosByOrder[$currentOrder])) {
                $photosByOrder[$currentOrder]['order'] = $newOrder;
            }
        }

        uasort($photosByOrder, function ($a, $b) {
            return $a['order'] <=> $b['order'];
        });

        $reorderedPhotos = array_values($photosByOrder);

        $media['photos'] = $reorderedPhotos;

        $user->update([
            'media' => $media
        ]);

        return response()->json([
            'message' => 'Media photos reordered successfully',
            'media' => $user->media
        ]);
    }

    /**
     * Get user profile by ID
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProfileById(Request $request, $id)
    {
        $user = User::findOrFail($id);

        $user->load('race.translations', 'personalities', 'level');

        $locale = App::getLocale();

        $race = null;
        if ($user->race) {
            $translation = $user->race->translations->where('locale', $locale)->first();
            $race = [
                'id' => $user->race->id,
                'name' => $translation ? $translation->name : $user->race->name,
                'description' => $translation ? $translation->description : $user->race->description
            ];
        }

        $personalities = $user->personalities->map(function ($personality) use ($locale) {
            $translation = $personality->translations->where('locale', $locale)->first();
            return [
                'id' => $personality->id,
                'name' => $translation ? $translation->name : $personality->name,
                'description' => $translation ? $translation->description : $personality->description
            ];
        });

        $followersCount = $user->followers()->count();
        $followingCount = $user->following()->count();

        $isFollowing = false;
        if ($request->user()) {
            $isFollowing = $request->user()->following()->where('following_id', $id)->exists();
        }

        return response()->json([
            'id' => $user->id,
            'mobile_number' => $user->mobile_number,
            'email' => $user->email,
            'country_code' => $user->country_code,
            'nickname' => $user->nickname,
            'gender' => $user->gender,
            'date_of_birth' => $user->date_of_birth ? $user->date_of_birth->format('Y-m-d\TH:i:s.v\Z') : null,
            'constellation' => $user->constellation,
            'profile_picture' => $user->profile_picture,
            'voice_note' => $user->voice_note,
            'biography' => $user->biography,
            'role' => $user->role,
            'experience' => $user->experience,
            'race' => $race,
            'height' => $user->height,
            'weight' => $user->weight,
            'default_language' => $user->default_language,
            'allow_3rd_party_access' => $user->allow_3rd_party_access,
            'referral_code' => $user->referral_code,
            'languages' => $user->languages->map(function ($language) {
                return [
                    'id' => $language->id,
                    'name' => $language->name
                ];
            }),
            'personalities' => $personalities,
            'level' => $user->level ? [
                'id' => $user->level->id,
                'level' => $user->level->level,
                'name' => $user->level->name,
                'description' => $user->level->description,
                'indicator_image' => $user->level->indicator_image
            ] : null,
            'total_followers' => $followersCount,
            'following_count' => $followingCount,
            'is_following' => $isFollowing
        ]);
    }

    /**
     * Get all profile information for the authenticated user
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAllProfile(Request $request)
    {
        $user = $request->user()->load([
            'race.translations',
            'personalities.translations',
            'languages',
            'level',
            'services.serviceCategory',
            'services.serviceType',
            'services.serviceStyles',
            'services.pricingOptions.pricingOptionType',
            'availabilities',
            'socialPosts' => function ($query) {
                $query->limit(10)->latest(); // Get latest 10 social posts
            },
            'missions' => function ($query) {
                $query->limit(10)->latest(); // Get latest 10 missions
            },
            'gameProfiles.serviceType' // Include all game profiles for logged-in user
        ]);

        return $this->formatCompleteProfile($user, $request->user());
    }

    /**
     * Get all profile information for a specific user by ID
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAllProfileById(Request $request, $id)
    {
        try {
            $user = User::with([
                'race.translations',
                'personalities.translations',
                'languages',
                'level',
                'services.serviceCategory',
                'services.serviceType',
                'services.serviceStyles',
                'services.pricingOptions.pricingOptionType',
                'availabilities',
                'socialPosts' => function ($query) {
                    $query->limit(10)->latest(); // Get latest 10 social posts
                },
                'missions' => function ($query) {
                    $query->limit(10)->latest(); // Get latest 10 missions
                },
                'activeGameProfiles.serviceType' // Include only non-hidden game profiles
            ])->findOrFail($id);

            return $this->formatCompleteProfile($user, $request->user());
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'message' => 'User not found',
                'errors' => ['user' => ['User not found']]
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'An error occurred while fetching user profile',
                'errors' => ['server' => [$e->getMessage()]]
            ], 500);
        }
    }

    /**
     * Format complete user profile with all details
     * 
     * @param User $user
     * @param User|null $currentUser
     * @return \Illuminate\Http\JsonResponse
     */
    private function formatCompleteProfile(User $user, ?User $currentUser)
    {
        $locale = App::getLocale();

        $race = null;
        if ($user->race) {
            $translation = $user->race->translations->where('locale', $locale)->first();
            $race = [
                'id' => $user->race->id,
                'name' => $translation ? $translation->name : $user->race->name,
                'description' => $translation ? $translation->description : $user->race->description
            ];
        }

        $personalities = $user->personalities->map(function ($personality) use ($locale) {
            $translation = $personality->translations->where('locale', $locale)->first();
            return [
                'id' => $personality->id,
                'name' => $translation ? $translation->name : $personality->name,
                'description' => $translation ? $translation->description : $personality->description
            ];
        });

        $followersCount = $user->followers()->count();

        $isFollowing = false;
        if ($currentUser && $currentUser->id !== $user->id) {
            $isFollowing = $currentUser->following()->where('following_id', $user->id)->exists();
        }

        $media = $user->media ?? [];
        if (is_string($media)) {
            try {
                $media = json_decode($media, true) ?? [];
            } catch (\Exception $e) {
                Log::error('Failed to decode user media JSON', [
                    'user_id' => $user->id,
                    'error' => $e->getMessage()
                ]);
                $media = [];
            }
        }

        $profileMedia = [
            'video' => $media['video'] ?? null,
            'thumbnail' => $media['thumbnail'] ?? null,
            'photos' => $media['photos'] ?? []
        ];

        $services = $user->services->map(function ($service) {
            $formatted = [
                'id' => $service->id,
                'total_person_order' => $service->total_person_order ?? 0,
                'created_at' => $service->created_at,
                'updated_at' => $service->updated_at,
                'service_category' => null,
                'service_type' => null,
                'service_styles' => null,
                'pricing_options' => null,
                'service_type_title' => null,
                'service_type_description' => null,
                'price' => null
            ];

            if (method_exists($service, 'serviceCategory') && $service->serviceCategory) {
                $formatted['service_category'] = [
                    'id' => $service->serviceCategory->id,
                    'name' => $service->serviceCategory->name,
                    'description' => $service->serviceCategory->description
                ];
            }

            if ($service->service_category_id == 2) { // Digital Services (OTHER_CATEGORY_ID)
                $formatted['service_type'] = null;
                $formatted['service_styles'] = null;
                $formatted['service_type_title'] = $service->service_type_title;
                $formatted['service_type_description'] = $service->service_type_description;
                $formatted['price'] = $service->price;
            } else {
                if (method_exists($service, 'serviceType') && $service->serviceType) {
                    $formatted['service_type'] = [
                        'id' => $service->serviceType->id,
                        'name' => $service->serviceType->name,
                        'description' => $service->serviceType->description,
                        'icon_path' => $service->serviceType->icon_path,
                        'required_elements' => $service->serviceType->required_elements,
                        'created_at' => $service->serviceType->created_at,
                        'updated_at' => $service->serviceType->updated_at,
                        'deleted_at' => $service->serviceType->deleted_at
                    ];
                }

                if (method_exists($service, 'serviceStyles')) {
                    $formatted['service_styles'] = $service->serviceStyles->map(function ($style) {
                        return [
                            'id' => $style->id,
                            'name' => $style->name,
                            'description' => $style->description,
                            'min_level_id' => $style->min_level_id,
                            'can_bypass' => $style->can_bypass,
                            'recommended_price' => $style->recommended_price,
                            'preset_price' => $style->preset_price,
                            'display_order' => $style->display_order,
                            'created_at' => $style->created_at,
                            'updated_at' => $style->updated_at,
                            'is_active' => (bool) $style->pivot->is_active,
                            'price' => (int) ($style->pivot->price ?? $style->preset_price)
                        ];
                    });
                }
            }

            if (method_exists($service, 'pricingOptions') && $service->pricingOptions->isNotEmpty()) {
                $pricingOption = $service->pricingOptions->first();
                
                if (method_exists($pricingOption, 'pricingOptionType') && $pricingOption->pricingOptionType) {
                    $pricingOptionType = $pricingOption->pricingOptionType;
                    $formatted['pricing_options'] = [
                        'id' => $pricingOptionType->id,
                        'name' => $pricingOptionType->name,
                        'description' => $pricingOptionType->description,
                        'is_active' => (bool) $pricingOption->is_active,
                        'display_order' => $pricingOptionType->display_order,
                        'created_at' => $pricingOptionType->created_at,
                        'updated_at' => $pricingOptionType->updated_at,
                        'deleted_at' => $pricingOptionType->deleted_at,
                        'has_duration' => (bool) $pricingOptionType->has_duration,
                        'unit' => $pricingOptionType->unit,
                        'quantity' => $pricingOptionType->quantity
                    ];
                }
            }

            return $formatted;
        });

        $gameProfiles = collect();
        if ($user->relationLoaded('gameProfiles')) {
            $gameProfiles = $user->gameProfiles;
        } elseif ($user->relationLoaded('activeGameProfiles')) {
            $gameProfiles = $user->activeGameProfiles;
        }

        $formattedGameProfiles = $gameProfiles->map(function ($profile) {
            return [
                'id' => $profile->id,
                'service_type_id' => $profile->service_type_id,
                'identifiers' => $profile->identifiers,
                'is_hidden' => $profile->is_hidden,
                'status' => $profile->status,
                'created_at' => $profile->created_at,
                'updated_at' => $profile->updated_at,
                'service_type' => $profile->serviceType ? [
                    'id' => $profile->serviceType->id,
                    'name' => $profile->serviceType->name,
                    'description' => $profile->serviceType->description,
                    'icon_path' => $profile->serviceType->icon_path,
                    'required_elements' => $profile->serviceType->required_elements
                ] : null
            ];
        });

        return response()->json([
            'id' => $user->id,
            'mobile_number' => $user->mobile_number,
            'email' => $user->email,
            'country_code' => $user->country_code,
            'nickname' => $user->nickname,
            'gender' => $user->gender,
            'date_of_birth' => $user->date_of_birth ? $user->date_of_birth->format('Y-m-d\TH:i:s.v\Z') : null,
            'constellation' => $user->constellation,
            'profile_picture' => $user->profile_picture,
            'profile_media' => $profileMedia,
            'voice_note' => $user->voice_note,
            'biography' => $user->biography,
            'role' => $user->role,
            'experience' => $user->experience,
            'height' => $user->height,
            'weight' => $user->weight,
            'default_language' => $user->languages->map(function ($language) {
                return [
                    'id' => $language->id,
                    'slug' => $language->slug,
                    'name' => $language->name
                ];
            }),
            'allow_3rd_party_access' => $user->allow_3rd_party_access,
            'referral_code' => $user->referral_code,
            'personalities' => $personalities,
            'level' => $user->level ? [
                'id' => $user->level->id,
                'level' => $user->level->level,
                'name' => $user->level->name,
                'description' => $user->level->description,
                'indicator_image' => $user->level->indicator_image
            ] : null,
            'is_follow' => $isFollowing,
            'total_followers' => $followersCount,
            'race' => $race,
            'services' => $services,
            'game_profiles' => $formattedGameProfiles,
            'user_availabilities' => $user->availabilities ? $user->availabilities->map(function ($availability) {
                return $availability;
            }) : [],
            'reviews' => [], // This could be expanded to include actual reviews if needed
            'social_posts' => $user->socialPosts ? $user->socialPosts : [],
            'missions' => $user->missions ? $user->missions : []
        ]);
    }
}
