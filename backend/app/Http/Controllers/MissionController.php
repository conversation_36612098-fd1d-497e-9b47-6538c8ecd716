<?php

namespace App\Http\Controllers;

use App\Models\Mission;
use App\Models\ServiceCategory;
use App\Models\User;
use App\Models\UserLevel;
use App\Rules\CleanContent;
use App\Services\InputSanitizationService;
use App\Services\MissionImageService;
use App\Services\MissionCreditService;
use App\Services\MissionCommissionService;
use App\Services\ReferralService;
use App\Services\ExperienceService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class MissionController extends Controller
{
    protected $inputSanitizationService;
    protected $missionImageService;
    protected $missionCreditService;
    protected $missionCommissionService;
    protected $referralService;
    protected $experienceService;

    public function __construct(
        InputSanitizationService $inputSanitizationService,
        MissionImageService $missionImageService,
        MissionCreditService $missionCreditService,
        MissionCommissionService $missionCommissionService,
        ReferralService $referralService = null,
        ExperienceService $experienceService = null
    ) {
        $this->inputSanitizationService = $inputSanitizationService;
        $this->missionImageService = $missionImageService;
        $this->missionCreditService = $missionCreditService;
        $this->missionCommissionService = $missionCommissionService;
        $this->referralService = $referralService;
        $this->experienceService = $experienceService;
    }

    /**
     * Display a listing of the missions.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = Mission::with([
            'serviceType.serviceCategory', 
            'serviceStyle', 
            'minLevel',
            'maxLevel',
            'images',
            'user' => function($query) {
                $query->select('id', 'name', 'gender', 'level_id', 'profile_picture', 'nickname');
                $query->with('level:id,name,level');
            }
        ]);
        
        if ($request->has('status')) {
            $query->where('status', $request->input('status'));
        }
        
        if ($request->has('type_id')) {
            $query->where('service_type_id', $request->input('type_id'));
        }
        
        if ($request->has('style_id')) {
            $query->where('service_style_id', $request->input('style_id'));
        }
        
        if ($request->has('min_level_id')) {
            $query->where('min_level_id', '<=', $request->input('min_level_id'));
        }
        
        $missions = $query->orderBy('created_at', 'desc')->paginate(15);
        
        $missions->through(function ($mission) {
            return $mission->makeHidden(['recommended_missions', 'remaining_slot']);
        });
        
        return response()->json($missions);
    }

    /**
     * Store a newly created mission.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $this->authorize('create', Mission::class);
        
        $validated = $request->validate([
            'service_type_id' => 'required|exists:service_types,id',
            'service_style_id' => 'required|exists:service_styles,id',
            'description' => ['required', 'string', 'max:500', new CleanContent],
            'bounty' => 'required|integer|min:1',
            'pax_required' => 'required|integer|min:1',
            'min_level_id' => 'required|exists:user_levels,id',
            'max_level_id' => 'nullable|exists:user_levels,id',
            'service_start_date' => 'required|date|after:now',
            'service_end_date' => 'nullable|date|after:service_start_date',
            'images' => 'sometimes|array|max:5',
            'images.*' => 'sometimes|file|image|max:20480', // 20MB limit
        ]);
        
        $totalCost = $validated['bounty'] * $validated['pax_required'];
        $commissionAmount = ($totalCost * $this->missionCommissionService->getDefaultCommission()) / 100;
        $totalCostWithCommission = $totalCost + $commissionAmount;
        
        // Check if user has enough credits to create the mission
        if ($request->user()->credits_balance < $totalCostWithCommission) {
            return response()->json([
                'message' => 'Insufficient credits to create this mission (including commission)',
                'details' => [
                    'total_cost' => $totalCost,
                    'commission_amount' => $commissionAmount,
                    'total_with_commission' => $totalCostWithCommission
                ]
            ], 422);
        }
        
        DB::beginTransaction();
        
        try {
            $sanitizedDescription = $this->inputSanitizationService->sanitize($validated['description']);
            
            // Count words in the description
            $wordCount = str_word_count($sanitizedDescription);
            if ($wordCount > 100) {
                return response()->json([
                    'message' => 'Description must not exceed 100 words.',
                    'errors' => [
                        'description' => ['Description must not exceed 100 words.']
                    ]
                ], 422);
            }
            
            // Create the mission
            $mission = $request->user()->missions()->create([
                'service_type_id' => $validated['service_type_id'],
                'service_style_id' => $validated['service_style_id'],
                'description' => $sanitizedDescription,
                'bounty' => $validated['bounty'],
                'pax_required' => $validated['pax_required'],
                'min_level_id' => $validated['min_level_id'],
                'max_level_id' => $validated['max_level_id'] ?? null,
                'service_start_date' => $validated['service_start_date'],
                'service_end_date' => $validated['service_end_date'] ?? null,
                'status' => 'open',
            ]);
            
            $this->missionCreditService->deductCreditsForMissionCreation($mission);
            
            // Process images if they exist
            if ($request->hasFile('images')) {
                $this->missionImageService->processMissionImages(
                    $mission, 
                    $request->file('images')
                );
            }
            DB::commit();
            
            $totalAmount = $mission->bounty * $mission->pax_required;
            $commissionAmount = $this->missionCommissionService->calculateCommission($mission);
            $totalWithCommission = $totalAmount + $commissionAmount;
            
            return response()->json([
                'message' => 'Mission created successfully',
                'mission' => $mission->load([
                    'serviceType', 
                    'serviceStyle', 
                    'minLevel', 
                    'maxLevel', 
                    'images',
                    'user' => function($query) {
                        $query->select('id', 'name', 'gender', 'level_id');
                        $query->with('level:id,name');
                    }
                ]),
                'payment_details' => [
                    'mission_cost' => $totalAmount,
                    'commission_amount' => $commissionAmount,
                    'total_paid' => $totalWithCommission,
                    'commission_percentage' => $this->missionCommissionService->getDefaultCommission()
                ]
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => 'Failed to create mission: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified mission.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $mission = Mission::with([
            'serviceType.serviceCategory', 
            'serviceStyle', 
            'minLevel',
            'maxLevel',
            'images',
            'user' => function($query) {
                $query->select('id', 'name', 'gender', 'level_id', 'profile_picture', 'nickname');
                $query->with('level:id,name,level');
            },
            'applicants' => function($query) {
                $query->with('user');
            }
        ])
        ->findOrFail($id);
        
        $this->authorize('view', $mission);
        
        return response()->json($mission);
    }

    /**
     * Update the specified mission.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $mission = Mission::findOrFail($id);
        
        $this->authorize('update', $mission);
        
        // Check if mission is already closed or completed
        if (!$mission->isPending()) {
            return response()->json([
                'message' => 'Only open missions can be updated',
            ], 422);
        }
        
        $validated = $request->validate([
            'service_type_id' => 'sometimes|exists:service_types,id',
            'service_style_id' => 'sometimes|exists:service_styles,id',
            'description' => ['sometimes', 'string', 'max:500', new CleanContent],
            'bounty' => 'sometimes|integer|min:1',
            'pax_required' => 'sometimes|integer|min:1',
            'min_level_id' => 'sometimes|exists:user_levels,id',
            'max_level_id' => 'sometimes|nullable|exists:user_levels,id',
            'service_start_date' => 'sometimes|date|after:now',
            'service_end_date' => 'sometimes|nullable|date|after:service_start_date',
            'status' => 'sometimes|in:open,closed,completed',
            'images' => 'sometimes|array|max:5',
            'images.*' => 'sometimes|file|image|max:20480', // 20MB limit
        ]);
        
        DB::beginTransaction();
        
        try {
            // If description is being updated, validate word count
            if (isset($validated['description'])) {
                $sanitizedDescription = $this->inputSanitizationService->sanitize($validated['description']);
                $wordCount = str_word_count($sanitizedDescription);
                
                if ($wordCount > 100) {
                    return response()->json([
                        'message' => 'Description must not exceed 100 words.',
                        'errors' => [
                            'description' => ['Description must not exceed 100 words.']
                        ]
                    ], 422);
                }
                
                $validated['description'] = $sanitizedDescription;
            }
            
            // Check if status is being changed to completed
            $statusChangedToCompleted = isset($validated['status']) && 
                                       $validated['status'] === 'completed' && 
                                       $mission->status !== 'completed';
            
            $mission->update($validated);
            
            // Process images if they exist
            if ($request->hasFile('images')) {
                $this->missionImageService->processMissionImages(
                    $mission, 
                    $request->file('images')
                );
            }
            
            // Process referral mission completion if status changed to completed
            if ($statusChangedToCompleted && $this->referralService) {
                try {
                    // Process referral points for mission poster
                    $poster = User::find($mission->user_id);
                    if ($poster) {
                        $this->referralService->processReferralMissionCompletion($poster);
                    }
                    
                    // Process referral points for approved applicants
                    $approvedApplicants = $mission->applicants()
                        ->where('status', 'approved')
                        ->get();
                        
                    foreach ($approvedApplicants as $applicant) {
                        $user = User::find($applicant->user_id);
                        if ($user) {
                            $this->referralService->processReferralMissionCompletion($user);
                        }
                    }
                } catch (\Exception $e) {
                    Log::error('Error processing referral mission completion points', [
                        'mission_id' => $mission->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            } else if ($statusChangedToCompleted && !$this->referralService) {
                Log::warning('ReferralService not available for mission completion processing', [
                    'mission_id' => $mission->id
                ]);
            }
            
            DB::commit();
            
            return response()->json([
                'message' => 'Mission updated successfully',
                'mission' => $mission->load([
                    'serviceType', 
                    'serviceStyle', 
                    'minLevel', 
                    'maxLevel', 
                    'images',
                    'user' => function($query) {
                        $query->select('id', 'name', 'gender', 'level_id');
                        $query->with('level:id,name');
                    }
                ]),
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => 'Failed to update mission: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified mission.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $mission = Mission::findOrFail($id);
        
        $this->authorize('delete', $mission);
        
        // Check if mission has applicants
        if ($mission->applicants()->count() > 0) {
            return response()->json([
                'message' => 'Cannot delete mission with applicants',
            ], 422);
        }
        
        $mission->delete();
        
        return response()->json([
            'message' => 'Mission deleted successfully',
        ]);
    }

    /**
     * Remove a specific image from a mission.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $mission_id
     * @param  int  $image_id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteImage(Request $request, $mission_id, $image_id)
    {
        $mission = Mission::findOrFail($mission_id);
        
        $this->authorize('update', $mission);
        
        // Check if mission is already closed or completed
        if (!$mission->isPending()) {
            return response()->json([
                'message' => 'Only open missions can be modified',
            ], 422);
        }
        
        $image = $mission->images()->findOrFail($image_id);
        
        try {
            // Delete the image from storage
            Storage::disk('cdn')->delete($image->original_path);
            Storage::disk('cdn')->delete($image->optimized_path);
            
            // Delete the image record
            $image->delete();
            
            return response()->json([
                'message' => 'Mission image deleted successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Failed to delete mission image: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Complete a mission and distribute credits to approved applicants.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function completeMission(Request $request, $id)
    {
        $mission = Mission::findOrFail($id);
        
        $this->authorize('update', $mission);
        
        if ($mission->isCompleted()) {
            return response()->json([
                'message' => 'Mission is already completed',
            ], 422);
        }
        
        if (!$mission->isClosed()) {
            return response()->json([
                'message' => 'Mission must be closed before it can be completed',
            ], 422);
        }
        
        DB::beginTransaction();
        
        try {
            $mission->update([
                'status' => 'completed',
            ]);
            
            // Get approved applicants
            $approvedApplicants = $mission->applicants()
                ->where('status', 'approved')
                ->get();
            
            foreach ($approvedApplicants as $applicant) {
                $this->missionCreditService->releaseCreditsToApplicant($mission, $applicant);
            }
            
            // Apply commission to system account only when mission is completed
            if ($mission->is_credit_held && $mission->commission_amount > 0 && $this->missionCommissionService) {
                $this->missionCommissionService->applyCommission($mission);
                
                $mission->update([
                    'is_credit_held' => false,
                ]);
            }
            
            // Process referral rewards if applicable
            if ($this->referralService) {
                try {
                    // Process referral for mission creator
                    $this->referralService->processReferralMissionCompletion($mission->user);
                    
                    // Also process referral for all approved applicants
                    foreach ($approvedApplicants as $applicant) {
                        if ($applicant->user) {
                            $this->referralService->processReferralMissionCompletion($applicant->user);
                        }
                    }
                } catch (\Exception $referralException) {
                    Log::error('Failed to process referral for mission completion', [
                        'mission_id' => $mission->id,
                        'error' => $referralException->getMessage(),
                        'trace' => $referralException->getTraceAsString(),
                    ]);
                }
            }
            
            if ($this->experienceService) {
                try {
                    $serviceType = $mission->serviceType;
                    $experiencePoints = $serviceType->experience ?? 0;
                    
                    if ($experiencePoints > 0) {
                        // Award experience to mission creator
                        if ($mission->user) {
                            $this->experienceService->awardExperience(
                                $mission->user,
                                $experiencePoints,
                                'Completed mission #' . $mission->id,
                                [
                                    'mission_id' => $mission->id,
                                    'service_type_id' => $serviceType->id,
                                    'service_type_name' => $serviceType->name,
                                    'role' => 'creator'
                                ]
                            );
                        }
                        
                        // Award experience to all approved applicants
                        foreach ($approvedApplicants as $applicant) {
                            if ($applicant->user) {
                                $this->experienceService->awardExperience(
                                    $applicant->user,
                                    $experiencePoints,
                                    'Completed mission #' . $mission->id,
                                    [
                                        'mission_id' => $mission->id,
                                        'service_type_id' => $serviceType->id,
                                        'service_type_name' => $serviceType->name,
                                        'role' => 'applicant'
                                    ]
                                );
                            }
                        }
                    }
                } catch (\Exception $e) {
                    Log::error('Error awarding experience for mission completion', [
                        'mission_id' => $mission->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            } else {
                Log::warning('ExperienceService not available for mission completion processing', [
                    'mission_id' => $mission->id
                ]);
            }
            
            DB::commit();
            
            return response()->json([
                'message' => 'Mission completed successfully',
                'mission' => $mission->load([
                    'serviceType', 
                    'serviceStyle', 
                    'minLevel', 
                    'maxLevel', 
                    'images',
                    'user' => function($query) {
                        $query->select('id', 'name', 'gender', 'level_id');
                        $query->with('level:id,name');
                    }
                ]),
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to complete mission', [
                'mission_id' => $id,
                'error_message' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return response()->json([
                'message' => 'Failed to complete mission: ' . $e->getMessage(),
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Cancel a mission and refund credits to the talent.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancelMission(Request $request, $id)
    {
        $mission = Mission::findOrFail($id);
        
        $this->authorize('update', $mission);
        
        // Check if mission can be cancelled based on time restriction
        [$canCancel, $reason] = $this->missionCreditService->canCancelMission($mission);
        
        if (!$canCancel) {
            return response()->json([
                'message' => $reason,
            ], 422);
        }
        
        DB::beginTransaction();
        
        try {
            Log::info('Mission state before cancellation', [
                'mission_id' => $mission->id,
                'status' => $mission->status,
                'is_cancelled' => $mission->isCancelled(),
            ]);
            
            $mission->update([
                'status' => 'cancelled',
            ]);
            
            $mission->refresh();
            
            Log::info('Mission state after status update', [
                'mission_id' => $mission->id,
                'status' => $mission->status,
                'is_cancelled' => $mission->isCancelled(),
            ]);
            
            [$refundSuccess, $refundMessage] = $this->missionCreditService->refundCreditsToCreator($mission, true);
            
            if (!$refundSuccess) {
                throw new \Exception($refundMessage);
            }
            
            // Release held credits for applicants
            $applicantsWithHeldCredits = $mission->applicants()
                ->where('is_credit_held', true)
                ->get();
            
            foreach ($applicantsWithHeldCredits as $applicant) {
                $applicant->update([
                    'is_credit_held' => false,
                    'credit_release_at' => now(),
                ]);
            }
            
            DB::commit();
            
            return response()->json([
                'message' => 'Mission cancelled successfully',
                'mission' => $mission->load([
                    'serviceType', 
                    'serviceStyle', 
                    'minLevel', 
                    'maxLevel', 
                    'images',
                    'user' => function($query) {
                        $query->select('id', 'name', 'gender', 'level_id');
                        $query->with('level:id,name');
                    }
                ]),
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to cancel mission', [
                'mission_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return response()->json([
                'message' => 'Failed to cancel mission: ' . $e->getMessage(),
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500);
        }
    }
}
