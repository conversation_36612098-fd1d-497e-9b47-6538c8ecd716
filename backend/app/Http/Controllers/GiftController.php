<?php

namespace App\Http\Controllers;

use App\Models\GiftItem;
use App\Models\User;
use App\Models\UserGift;
use App\Services\GiftService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Validator;

class GiftController extends Controller
{
    protected $giftService;
    
    public function __construct(GiftService $giftService)
    {
        $this->giftService = $giftService;
    }
    
    /**
     * Get available gift items for the authenticated user.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAvailableGiftItems(Request $request)
    {
        $locale = App::getLocale();
        $giftItems = $this->giftService->getAvailableGiftItems($request->user(), $locale);
        
        return response()->json([
            'gift_items' => $giftItems
        ]);
    }
    
    /**
     * Purchase a gift item for the authenticated user.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function purchaseGiftItem(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'gift_item_id' => 'required|exists:gift_items,id',
            'quantity' => 'integer|min:1',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }
        
        try {
            $giftItem = GiftItem::findOrFail($request->input('gift_item_id'));
            $quantity = $request->input('quantity', 1);
            $userGift = $this->giftService->purchaseGift($request->user(), $giftItem, $quantity);
            
            return response()->json([
                'message' => 'Gift purchased successfully',
                'user_gift' => $userGift->load('giftItem')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => $e->getMessage()
            ], 400);
        }
    }
    
    /**
     * Get gift inventory for the authenticated user.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserGifts(Request $request)
    {
        $locale = App::getLocale();
        
        $userGifts = $request->user()->gifts()
            ->with(['giftItem.translations' => function ($query) use ($locale) {
                $query->where('locale', $locale);
            }, 'giftedBy'])
            ->orderBy('acquired_at', 'desc')
            ->get()
            ->map(function ($userGift) {
                $giftItem = $userGift->giftItem;
                $translation = $giftItem->translations->first();
                
                return [
                    'id' => $userGift->id,
                    'gift_item_id' => $giftItem->id,
                    'name' => $translation?->name ?? $giftItem->name,
                    'description' => $translation?->description ?? $giftItem->description,
                    'icon_path' => $giftItem->icon_path,
                    'acquired_at' => $userGift->acquired_at,
                    'can_sell' => $userGift->can_sell,
                    'sell_back_price' => $giftItem->sell_back_price,
                    'quantity' => $userGift->quantity,
                    'is_giftable' => $userGift->gifted_by_user_id === null,
                    'gifted_by' => $userGift->giftedBy ? [
                        'id' => $userGift->giftedBy->id,
                        'nickname' => $userGift->giftedBy->nickname
                    ] : null
                ];
            });
        
        // Get inventory statistics
        $statistics = $this->giftService->getGiftInventoryStatistics($request->user());
        
        return response()->json([
            'user_gifts' => $userGifts,
            'gift_inventory_statistics' => $statistics
        ]);
    }
    
    /**
     * Sell a gift back to the platform for credits.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function sellGift(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'quantity' => 'integer|min:1',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }
        
        try {
            $userGift = UserGift::where('user_id', $request->user()->id)
                ->where('id', $id)
                ->firstOrFail();
            
            $quantity = $request->input('quantity', 1);
            
            $this->giftService->sellGift($userGift, $quantity);
            
            return response()->json([
                'message' => 'Gift sold successfully',
                'credits_balance' => $request->user()->credits_balance
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => $e->getMessage()
            ], 400);
        }
    }
    
    /**
     * Gift an item to another user.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function giftToUser(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_gift_id' => 'required|exists:user_gifts,id',
            'recipient_user_id' => 'required|exists:users,id',
            'quantity' => 'required|integer|min:1',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }
        
        try {
            $userGift = UserGift::where('user_id', $request->user()->id)
                ->where('id', $request->input('user_gift_id'))
                ->firstOrFail();
                
            $recipientUser = User::findOrFail($request->input('recipient_user_id'));
            
            // Prevent gifting to self
            if ($recipientUser->id === $request->user()->id) {
                return response()->json([
                    'message' => 'You cannot gift items to yourself'
                ], 400);
            }
            
            $quantity = $request->input('quantity');
            
            $recipientGift = $this->giftService->giftToUser(
                $request->user(),
                $recipientUser,
                $userGift,
                $quantity
            );
            
            return response()->json([
                'message' => 'Gift sent successfully',
                'recipient_gift' => $recipientGift->load('giftItem')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => $e->getMessage()
            ], 400);
        }
    }
    
    /**
     * Get total gifts received by the authenticated user.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTotalGiftsReceived(Request $request)
    {
        $statistics = $this->giftService->getTotalGiftsReceived($request->user());
        
        return response()->json($statistics);
    }
    
    /**
     * Get gift contributors for the authenticated user.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getGiftContributors(Request $request)
    {
        $contributors = $this->giftService->getGiftContributors($request->user());
        
        return response()->json([
            'contributors' => $contributors
        ]);
    }
    
    /**
     * Get gift inventory statistics for the authenticated user.
     * Shows giftable (pre-purchased) vs non-giftable (received) gift items.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getGiftInventoryStatistics(Request $request)
    {
        $statistics = $this->giftService->getGiftInventoryStatistics($request->user());
        
        return response()->json($statistics);
    }
    
    /**
     * Redeem a gift item using referral points.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function redeemGiftWithPoints(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'gift_item_id' => 'required|exists:gift_items,id',
            'quantity' => 'integer|min:1',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }
        
        try {
            $giftItem = GiftItem::findOrFail($request->input('gift_item_id'));
            $quantity = $request->input('quantity', 1);
            $userGift = $this->giftService->redeemGiftWithPoints($request->user(), $giftItem, $quantity);
            
            return response()->json([
                'message' => 'Gift redeemed successfully with points',
                'user_gift' => $userGift->load('giftItem'),
                'points_balance' => $request->user()->points_balance
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => $e->getMessage()
            ], 400);
        }
    }
    
    /**
     * Get gift transaction history for the authenticated user.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getGiftTransactionHistory(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'limit' => 'integer|min:1|max:100',
            'offset' => 'integer|min:0',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }
        
        $limit = $request->input('limit', 10);
        $offset = $request->input('offset', 0);
        
        $transactions = $this->giftService->getTransactionHistory(
            $request->user(),
            $limit,
            $offset
        );
        
        // Format the transactions for the response
        $formattedTransactions = $transactions->map(function ($transaction) {
            $giftItem = $transaction->giftItem;
            
            return [
                'id' => $transaction->id,
                'transaction_type' => $transaction->transaction_type,
                'gift_item' => [
                    'id' => $giftItem->id,
                    'name' => $giftItem->name,
                    'icon_path' => $giftItem->icon_path
                ],
                'quantity' => $transaction->quantity,
                'payment_method' => $transaction->payment_method,
                'points_spent' => $transaction->points_spent,
                'credits_spent' => $transaction->credits_spent,
                'metadata' => $transaction->metadata,
                'created_at' => $transaction->created_at
            ];
        });
        
        return response()->json([
            'transactions' => $formattedTransactions,
            'pagination' => [
                'total' => GiftTransaction::where('user_id', $request->user()->id)->count(),
                'limit' => $limit,
                'offset' => $offset
            ]
        ]);
    }
}
