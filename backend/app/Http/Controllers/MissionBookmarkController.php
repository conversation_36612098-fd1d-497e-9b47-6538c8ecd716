<?php

namespace App\Http\Controllers;

use App\Models\Mission;
use App\Models\MissionBookmark;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MissionBookmarkController extends Controller
{
    /**
     * Display a listing of the bookmarked missions for the authenticated user.
     */
    public function index()
    {
        $bookmarkedMissions = Auth::user()->bookmarkedMissions()
            ->with(['serviceType', 'serviceStyle', 'minLevel', 'maxLevel', 'user'])
            ->orderBy('mission_bookmarks.created_at', 'desc')
            ->paginate(10);

        return response()->json($bookmarkedMissions);
    }

    /**
     * Toggle bookmark status for a mission.
     */
    public function toggleBookmark(Request $request, Mission $mission_id)
    {
        $userId = Auth::id();
        $bookmark = MissionBookmark::where('user_id', $userId)
            ->where('mission_id', $mission_id->id)
            ->first();

        if ($bookmark) {
            $bookmark->delete();
            $action = 'unbookmarked';
        } else {
            MissionBookmark::create([
                'user_id' => $userId,
                'mission_id' => $mission_id->id,
            ]);
            $action = 'bookmarked';
        }

        return response()->json(['action' => $action]);
    }
}
