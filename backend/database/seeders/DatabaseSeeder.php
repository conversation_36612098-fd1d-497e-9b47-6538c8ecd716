<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Seed Malaysian banks first
        $this->call(MalaysianBankSeeder::class);

        User::factory(500)->create();

        // foreach (range(1, 10) as $i) { // 10 batches of 10,000 users
        //     \App\Models\User::factory(10000)->create();
        //     $this->command->info("Batch $i completed.");
        //     \Faker\Factory::create()->unique(true); // Reset unique memory
        // }
    }
}
