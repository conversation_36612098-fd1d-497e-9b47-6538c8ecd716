<?php

use App\Http\Controllers\Auth\{<PERSON>gin<PERSON><PERSON>roller, RegisterController, OAuthController, ForgotPasswordController};
use App\Http\Controllers\{BackOfficeController,BackOfficeAvailabilityController,BackOfficeDisputeController,BackOfficeDisputeTypeController,BackOfficeGiftController,BackOfficeStaticContentController,CarouselSlideController,ChatController,CommissionController,CreditController,DeviceTokenController,DisputeController,EKycController,EmailVerificationController,EmergencyContactController,FeedbackController,GiftController,LanguageController,LevelController,MissionController,MissionApplicantController,MissionBookmarkController,OrderController,PaymentController,PersonalityController,PlacesController,PointController,RaceController,ReferralController,ScheduledOrderController,ServiceConfigurationController,SmsDeliveryNotificationController,SocialPostController,SocialPostCommentController,StaticContentController,TalentFilterController,UserAvailabilityController,UserBankAccountController,UserFollowerController,UserGameProfileController,UserProfileController,UserReviewController,UserServiceController,UserSettingController,WithdrawalController};
use App\Http\Middleware\BackofficeAuthMiddleware;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Support\Facades\Route;

Route::prefix('auth')->group(function () {
    // Authentication routes
    Route::post('request-otp', [RegisterController::class, 'requestOtp'])
        ->middleware('throttle:1,1'); // 60s cooldown
    Route::post('login', [LoginController::class, 'login'])->middleware(StartSession::class);
    Route::post('register', [RegisterController::class, 'register']);

    Route::post('logout', [LoginController::class, 'logout'])
        ->middleware(['auth:api', StartSession::class]);

    Route::post('verify-otp-for-reset', [ForgotPasswordController::class, 'verifyOtp']);
    Route::post('reset-password', [ForgotPasswordController::class, 'resetPassword']);
    Route::middleware('auth:api')->group(function () {
        Route::post('user/device-token', [DeviceTokenController::class, 'store']);
        Route::delete('user/device-token', [DeviceTokenController::class, 'destroy']);
    });

    // OAuth routes
    Route::get('oauth/{provider}/redirect', [OAuthController::class, 'redirect'])
        ->middleware('web')
        ->where('provider', 'google|apple');
    Route::get('oauth/{provider}/callback', [OAuthController::class, 'callback'])
        ->middleware('web')
        ->where('provider', 'google|apple');
});

Route::get('email/verify', [EmailVerificationController::class, 'verify'])
    ->name('email.verify');
Route::post('email/send-verification', [EmailVerificationController::class, 'sendVerification'])
    ->middleware('auth:api');

// Protected routes
Route::middleware('auth:api')->group(function () {
    Route::get('user', function () {
        return auth()->user();
    });

    Route::prefix('orders')->group(function () {
        Route::post('/', [OrderController::class, 'orderNow']);
        Route::post('/{orderId}/respond', [OrderController::class, 'respondToOrder']);
        Route::post('/{orderId}/complete', [OrderController::class, 'completeOrder']);
        Route::post('/{orderId}/cancel', [OrderController::class, 'cancelOrder']);
        Route::post('/{orderId}/reviews', [OrderController::class, 'createReview']);
        Route::get('/{orderId}/reviews', [OrderController::class, 'getReviews']);
        Route::post('/{orderId}/disputes', [OrderController::class, 'disputeOrder']);
        Route::get('/{orderId}', [OrderController::class, 'getOrder']);
        Route::get('/', [OrderController::class, 'getUserOrders']);
        Route::get('/{orderId}/check-overtime', [OrderController::class, 'checkOrderOvertime']);
        Route::get('/talents/{talentId}/wait-time', [OrderController::class, 'getEstimatedWaitTime']);
    });

    Route::prefix('scheduled-orders')->group(function () {
        Route::post('/', [ScheduledOrderController::class, 'orderForLater']);
        Route::post('/{orderId}/respond', [ScheduledOrderController::class, 'respondToScheduledOrder']);
        Route::post('/{orderId}/complete', [ScheduledOrderController::class, 'completeScheduledOrder']);
        Route::post('/{orderId}/reviews', [ScheduledOrderController::class, 'createReview']);
        Route::get('/{orderId}/reviews', [ScheduledOrderController::class, 'getReviews']);
        Route::post('/{orderId}/disputes', [ScheduledOrderController::class, 'disputeScheduledOrder']);
        Route::post('/{orderId}/cancel', [ScheduledOrderController::class, 'cancelScheduledOrder']);
        Route::get('/{orderId}', [ScheduledOrderController::class, 'getScheduledOrder']);
        Route::get('/', [ScheduledOrderController::class, 'getUserScheduledOrders']);
        Route::get('/talents/{talentId}/availability', [ScheduledOrderController::class, 'getTalentAvailability']);
        Route::get('/talents/{talentId}/special-hours', [ScheduledOrderController::class, 'getTalentSpecialHours']);
        Route::get('/talents/{talentId}/available-time-slots', [ScheduledOrderController::class, 'getTalentAvailableTimeSlots']);
        Route::post('/check-availability', [ScheduledOrderController::class, 'checkTalentAvailability']);
    });

    Route::prefix('disputes')->group(function () {
        Route::get('/types', [DisputeController::class, 'getDisputeTypes']);
        Route::post('/', [DisputeController::class, 'createDispute']);
        Route::get('/', [DisputeController::class, 'getUserDisputes']);
        Route::get('/{id}', [DisputeController::class, 'getDispute']);
    });

    // Social Media Posting Module Routes
    Route::middleware(['throttle:social-posts'])->group(function () {
        // Special routes must be defined before resource routes to avoid conflicts
        Route::get('social-posts/random', [SocialPostController::class, 'random']);
        Route::get('social-posts/feed', [SocialPostController::class, 'paginatedFeed']);

        Route::apiResource('social-posts', SocialPostController::class);
        Route::put('social-posts/{socialPost}/hide', [SocialPostController::class, 'hidePost']);
        Route::post('social-posts/{socialPost}/like', [SocialPostController::class, 'toggleLike']);

        // Social Post Comments
        Route::apiResource('social-posts.comments', SocialPostCommentController::class);

        // Google Places API
        Route::get('places/search', [PlacesController::class, 'search']);
        Route::get('places/details', [PlacesController::class, 'details']);
    });

    // Personality management
    Route::get('personalities', [PersonalityController::class, 'index']);
    Route::get('users/personalities', [PersonalityController::class, 'getUserPersonalities']);
    Route::post('users/personalities', [PersonalityController::class, 'updateUserPersonalities']);

    // Language management
    Route::get('languages', [LanguageController::class, 'index']);
    Route::get('languages/{id}', [LanguageController::class, 'show']);
    Route::get('users/languages', [LanguageController::class, 'getUserLanguages']);
    Route::post('users/languages', [LanguageController::class, 'updateUserLanguages']);

    // Emergency contacts management
    Route::apiResource('emergency-contacts', EmergencyContactController::class);

    // User profile management
    Route::get('user/profile', [UserProfileController::class, 'getProfile']);
    Route::get('user/profile/{id}', [UserProfileController::class, 'getProfileById']);
    Route::put('user/profile', [UserProfileController::class, 'updateProfile']);
    Route::post('user/profile-picture', [UserProfileController::class, 'updateProfilePicture']);
    Route::get('user/biography', [UserProfileController::class, 'getBiography']);
    Route::put('user/biography', [UserProfileController::class, 'updateBiography']);
    Route::get('user/allow-3rd-party-access', [UserProfileController::class, 'getAllow3rdPartyAccess']);
    Route::put('user/allow-3rd-party-access', [UserProfileController::class, 'updateAllow3rdPartyAccess']);
    Route::post('user/voice-note', [UserProfileController::class, 'uploadVoiceNote']);
    Route::delete('user/voice-note', [UserProfileController::class, 'deleteVoiceNote']);

    Route::get('user/all-profile', [UserProfileController::class, 'getAllProfile']);
    Route::get('user/all-profile/{id}', [UserProfileController::class, 'getAllProfileById']);

    Route::post('user/media', [UserProfileController::class, 'uploadMedia']);
    Route::delete('user/media', [UserProfileController::class, 'deleteMedia']);
    Route::put('user/media/reorder', [UserProfileController::class, 'reorderMedia']);

    // Referral management
    Route::get('referrals', [ReferralController::class, 'getReferredUsers']);
    Route::post('referrals/search', [ReferralController::class, 'getReferredUsersByCode']);

    // Points management
    Route::get('points/transactions', [PointController::class, 'getTransactionHistory']);

    // Level management
    Route::get('levels', [LevelController::class, 'getLevels']);
    Route::get('user/level', [LevelController::class, 'getUserLevel']);
    Route::get('user/experience/history', [LevelController::class, 'getExperienceHistory']);

    // User availability management
    Route::get('user/availability', [UserAvailabilityController::class, 'index']);
    Route::post('user/availability', [UserAvailabilityController::class, 'store']);
    Route::get('user/availability/{availability}', [UserAvailabilityController::class, 'show']);
    Route::put('user/availability/{availability}', [UserAvailabilityController::class, 'update']);
    Route::delete('user/availability/{availability}', [UserAvailabilityController::class, 'destroy']);
    Route::post('user/availability/batch', [UserAvailabilityController::class, 'batchUpdate']);
    Route::get('user/availability/status', [UserAvailabilityController::class, 'getStatus']);

    // User availability override
    Route::post('user/availability/override', [UserAvailabilityController::class, 'setOverride']);
    Route::get('user/availability/override', [UserAvailabilityController::class, 'getOverride']);
    Route::delete('user/availability/override', [UserAvailabilityController::class, 'removeOverride']);

    // Feedback management
    Route::post('feedback', [FeedbackController::class, 'submitFeedback'])
        ->middleware('throttle:10,1'); // 10 requests per minute
    Route::get('feedback/topics', [FeedbackController::class, 'getTopics']);

    // Gift inventory routes
    Route::get('gifts', [GiftController::class, 'getAvailableGiftItems']);
    Route::post('gifts/purchase', [GiftController::class, 'purchaseGiftItem']);
    Route::post('gifts/redeem', [GiftController::class, 'redeemGiftWithPoints']);
    Route::get('user/gifts', [GiftController::class, 'getUserGifts']);
    Route::post('user/gifts/{id}/sell', [GiftController::class, 'sellGift']);
    Route::post('user/gifts/gift', [GiftController::class, 'giftToUser']);
    Route::get('user/gifts/statistics', [GiftController::class, 'getTotalGiftsReceived']);
    Route::get('user/gifts/contributors', [GiftController::class, 'getGiftContributors']);
    Route::get('user/gifts/inventory-statistics', [GiftController::class, 'getGiftInventoryStatistics']);
    Route::get('user/gifts/transactions', [GiftController::class, 'getGiftTransactionHistory']);

    // Service Configuration
    Route::prefix('service-configuration')->group(function () {
        Route::get('categories', [ServiceConfigurationController::class, 'getServiceCategories']);
        Route::get('types', [ServiceConfigurationController::class, 'getServiceTypes']);
        Route::get('styles', [ServiceConfigurationController::class, 'getServiceStyles']);
        Route::get('pricing-option-types', [ServiceConfigurationController::class, 'getPricingOptionTypes']);
        Route::get('service-links', [ServiceConfigurationController::class, 'getServiceLinks']);
    });

    // User Services
    Route::prefix('user/services')->group(function () {
        Route::get('/', [UserServiceController::class, 'index']);
        Route::post('/', [UserServiceController::class, 'store']);
        Route::get('/{id}', [UserServiceController::class, 'show']);
        Route::get('/user/{userId}/approved', [UserServiceController::class, 'getApprovedServices']);
        Route::put('/', [UserServiceController::class, 'update']);
        Route::put('/{id}/toggle-active', [UserServiceController::class, 'toggleActive']);
    });

    Route::prefix('talents')->group(function () {
        Route::get('/', [TalentFilterController::class, 'filterTalents']);
        Route::get('/service-types', [TalentFilterController::class, 'getFilteredServiceTypes']);
        Route::get('/service-styles', [TalentFilterController::class, 'getFilteredServiceStyles']);
    });

    // Mission routes
    Route::prefix('missions')->group(function () {
        Route::get('/', [MissionController::class, 'index']);
        Route::post('/', [MissionController::class, 'store']);
        Route::get('/{id}', [MissionController::class, 'show']);
        Route::put('/{id}', [MissionController::class, 'update']);
        Route::delete('/{id}', [MissionController::class, 'destroy']);
        Route::post('/{id}/complete', [MissionController::class, 'completeMission']);
        Route::post('/{id}/cancel', [MissionController::class, 'cancelMission']);

        // Add this new route for deleting images
        Route::delete('/{mission_id}/images/{image_id}', [MissionController::class, 'deleteImage']);

        // Mission applicants routes
        Route::post('/{mission_id}/apply', [MissionApplicantController::class, 'apply']);
        Route::get('/{mission_id}/applicants', [MissionApplicantController::class, 'getApplicants']);
        Route::post('/{mission_id}/applicants/{child_id}/approve', [MissionApplicantController::class, 'approveApplicant']);
        Route::post('/{mission_id}/applicants/{child_id}/reject', [MissionApplicantController::class, 'rejectApplicant']);

        // Mission bookmarks routes
        Route::post('/{mission_id}/bookmark', [MissionBookmarkController::class, 'toggleBookmark']);
        Route::get('/user/bookmarked-missions', [MissionBookmarkController::class, 'index']);

        // Mission dispute route
        Route::post('/{mission}/disputes', MissionDisputeController::class);
    });

    Route::prefix('users')->group(function () {
        Route::get('/{userId}/reviews', [UserReviewController::class, 'getUserReviews']);

        Route::post('/{userId}/follow', [UserFollowerController::class, 'toggleFollow']);
        Route::get('/{userId}/followers', [UserFollowerController::class, 'getFollowers']);
        Route::get('/{userId}/following', [UserFollowerController::class, 'getFollowing']);
    });

    // Malaysian Banks
    Route::get('malaysian-banks', [App\Http\Controllers\MalaysianBankController::class, 'index']);

    // User Bank Accounts
    Route::prefix('user/bank-accounts')->group(function () {
        Route::get('/', [UserBankAccountController::class, 'index']);
        Route::post('/', [UserBankAccountController::class, 'store']);
        Route::put('/{id}', [UserBankAccountController::class, 'update']);
        Route::delete('/{id}', [UserBankAccountController::class, 'destroy']);
        Route::post('/{id}/set-primary', [UserBankAccountController::class, 'setPrimary']);
    });

    Route::prefix('withdrawals')->middleware([\App\Http\Middleware\EnsureEkycVerified::class, \App\Http\Middleware\EnsureEmailVerified::class])->group(function () {
        Route::post('/credits', [WithdrawalController::class, 'withdrawCredits']);
        Route::get('/currencies', [WithdrawalController::class, 'getAvailableCurrencies']);
        Route::get('/history', [WithdrawalController::class, 'getTransactionHistory']);
        Route::get('/{id}', [WithdrawalController::class, 'getTransaction']);
    });

    Route::prefix('gifts')->middleware(['auth:api'])->group(function () {
        Route::post('/exchange', [WithdrawalController::class, 'exchangeGiftForCredits']);
    });

    Route::prefix('chat')->group(function () {
        Route::get('/conversations', [ChatController::class, 'getConversations']);
        Route::get('/conversations/{conversationId}/messages', [ChatController::class, 'getMessages']);
        Route::post('/conversations', [ChatController::class, 'startConversation']);
        Route::post('/messages/text', [ChatController::class, 'sendTextMessage']);
        Route::post('/messages/attachment', [ChatController::class, 'sendMessageWithAttachment']);
        Route::post('/messages/status', [ChatController::class, 'updateMessageStatus']);
    });

    Route::prefix('ekyc')->group(function () {
        Route::post('/malaysian', [EKycController::class, 'verifyMalaysian']);
        Route::post('/foreigner', [EKycController::class, 'verifyForeigner']);
        Route::get('/status', [EKycController::class, 'getVerificationStatus']);
        Route::get('/history', [EKycController::class, 'getVerificationHistory']);
        Route::get('/methods', [EKycController::class, 'getAvailableMethods']);

        Route::middleware('backoffice.auth')->group(function () {
            Route::get('/manual/pending', [EKycController::class, 'getPendingManualVerifications']);
            Route::post('/manual/{id}/approve', [EKycController::class, 'approveManualVerification']);
            Route::post('/manual/{id}/reject', [EKycController::class, 'rejectManualVerification']);
        });
    });
});

// SMS Delivery Notification webhook
Route::post('sms/delivery-notification', [SmsDeliveryNotificationController::class, 'handleDeliveryNotification']);

Route::middleware('auth:api')->group(function () {
    Route::post('payments', [PaymentController::class, 'createPayment']);
    Route::post('payments/retry', [PaymentController::class, 'retryPayment']);

    Route::apiResource('user-game-profiles', UserGameProfileController::class);
    Route::put('user-game-profiles/{id}/visible', [UserGameProfileController::class, 'setVisible']);
    Route::post('payments/callback', [PaymentController::class, 'handleCallback'])->name('api.payments.callback');
    Route::get('payments/return', [PaymentController::class, 'handleReturn'])->name('api.payments.return');
    // Credit module routes
    Route::prefix('credits')->group(function () {
        //Route::get('channels', [CreditController::class, 'getAvailableChannels']);
        Route::get('balance', [CreditController::class, 'getBalance']);
        Route::get('transactions', [CreditController::class, 'getTransactionHistory']);

        Route::prefix('{channel}')->group(function () {
            Route::get('packages', [CreditController::class, 'getAvailablePackages']);
        });
    });
});


// Race endpoints
Route::get('races', [RaceController::class, 'index']);

// Static content endpoints
Route::prefix('static-content')->group(function () {
    Route::get('terms-conditions', [StaticContentController::class, 'getTermsConditions']);
    Route::get('privacy-policy', [StaticContentController::class, 'getPrivacyPolicy']);
    Route::get('about-us', [StaticContentController::class, 'getAboutUs']);
    Route::get('contact-us', [StaticContentController::class, 'getContactUs']);
    Route::get('allow-3rd-party-access', [UserSettingController::class, 'getAllow3rdPartyAccess']);
});

Route::apiResource('carousel-slides', CarouselSlideController::class);

Route::prefix('homepage')->group(function () {
    Route::get('/', [App\Http\Controllers\HomeController::class, 'getHomepageData']);

    Route::get('new-talents', [App\Http\Controllers\HomeController::class, 'getNewRegisteredTalents']);
    Route::get('recommended-talents', [App\Http\Controllers\HomeController::class, 'getRecommendedTalents']);
    Route::get('online-talents', [App\Http\Controllers\HomeController::class, 'getOnlineTalents']);
    Route::get('available-missions-count', [App\Http\Controllers\HomeController::class, 'getAvailableMissionsCount']);
    Route::get('popular-service-types', [App\Http\Controllers\HomeController::class, 'getPopularServiceTypes']);
});
